/**
 * Provider Profile API Handlers
 * Handles provider profile management endpoints
 */

import type { Request, Response } from 'express';
import {
  sendSuccess,
  sendError,
  asyncHandler,
  validateAndExtract,
  updateProviderProfileSchema,
  type ProviderProfileResponse,
  type UpdateProviderProfileRequest
} from '../index';
import { getUserServiceProvider, updateSProvider } from '../../operations';

/**
 * GET /api/providers/profile
 * Get authenticated provider's profile
 */
export const getProviderProfile = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Fetch provider profile using existing operation
    const provider = await getUserServiceProvider({ userId: context.user.id }, context);

    if (!provider) {
      return sendError(res, { statusCode: 404, message: 'Provider profile not found' });
    }

    // Format response according to API standards
    const response: ProviderProfileResponse = {
      id: provider.id,
      userId: provider.userId,
      title: provider.title || undefined,
      phone: provider.phone || undefined,
      presentation: provider.presentation || undefined,
      isVerified: provider.isVerified,
      isSetupComplete: provider.isSetupComplete,
      category: provider.category ? {
        id: provider.category.id,
        title: provider.category.title
      } : undefined,
      averageRating: provider.averageRating || undefined,
      totalReviews: provider.totalReviews || 0
    };

    sendSuccess(res, response, 'Provider profile retrieved successfully');
  } catch (error: any) {
    console.error('[getProviderProfile] Error:', error);
    sendError(res, error, 'Failed to retrieve provider profile');
  }
});

/**
 * PUT /api/providers/profile
 * Update authenticated provider's profile
 */
export const updateProviderProfile = asyncHandler(async (req: Request, res: Response, context: any) => {
  try {
    // Verify user is authenticated
    if (!context.user) {
      return sendError(res, { statusCode: 401, message: 'Authentication required' });
    }

    // Validate request data
    const updateData = validateAndExtract(updateProviderProfileSchema, req.body);
    console.log('provider updateData', updateData);
    // Update provider using existing operation
    const updatedProvider = await updateSProvider(updateData, context);


    console.log('provider updatedProvider', updatedProvider);


    // Fetch updated provider with category info
    const providerWithCategory = await getUserServiceProvider({ userId: context.user.id }, context);

    // Format response according to API standards
    const response: ProviderProfileResponse = {
      id: updatedProvider.id,
      userId: updatedProvider.userId,
      title: updatedProvider.title || undefined,
      phone: updatedProvider.phone || undefined,
      presentation: updatedProvider.presentation || undefined,
      isVerified: updatedProvider.isVerified,
      isSetupComplete: updatedProvider.isSetupComplete,
      category: providerWithCategory?.category ? {
        id: providerWithCategory.category.id,
        title: providerWithCategory.category.title
      } : undefined,
      averageRating: updatedProvider.averageRating || undefined,
      totalReviews: updatedProvider.totalReviews || 0
    };

    sendSuccess(res, response, 'Provider profile updated successfully');
  } catch (error: any) {
    console.error('[updateProviderProfile] Error:', error);
    sendError(res, error, 'Failed to update provider profile');
  }
});
